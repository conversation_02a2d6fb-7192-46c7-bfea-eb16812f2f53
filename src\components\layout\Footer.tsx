
import { Link } from "react-router-dom";
import { Facebook, Twitter, Instagram, Mail, MapPin, Phone } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-agro-900 text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">CMC Agriculture</h3>
            <p className="text-gray-300 mb-4">
              Le Pôle Agriculture de la Cité des Métiers et des Compétences (CMC) propose des formations de qualité pour les futurs professionnels du secteur agricole.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Instagram size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Navigation</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors">
                  Accueil
                </Link>
              </li>
              <li>
                <Link to="/presentation" className="text-gray-300 hover:text-white transition-colors">
                  Présentation
                </Link>
              </li>
              <li>
                <Link to="/formations" className="text-gray-300 hover:text-white transition-colors">
                  Formations
                </Link>
              </li>
              <li>
                <Link to="/actualites" className="text-gray-300 hover:text-white transition-colors">
                  Actualités
                </Link>
              </li>
              <li>
                <Link to="/espace-etudiant" className="text-gray-300 hover:text-white transition-colors">
                  Espace Étudiant
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Formations</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Culture
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Élevage
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Gestion d'exploitation
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Agriculture durable
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Toutes les formations
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="mr-2 h-5 w-5 text-agro-400 flex-shrink-0 mt-0.5" />
                <span className="text-gray-300">123 Avenue de l'Agriculture, Maroc</span>
              </li>
              <li className="flex items-center">
                <Phone className="mr-2 h-5 w-5 text-agro-400" />
                <span className="text-gray-300">+212 5XX XX XX XX</span>
              </li>
              <li className="flex items-center">
                <Mail className="mr-2 h-5 w-5 text-agro-400" />
                <span className="text-gray-300"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-6 border-t border-gray-700 text-center">
          <p className="text-gray-400">
            &copy; {currentYear} Pôle Agriculture - Cité des Métiers et des Compétences. Tous droits réservés.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
