
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  icon: React.ReactNode;
  value: string | number;
  label: string;
  className?: string;
}

const StatsCard = ({ icon, value, label, className }: StatsCardProps) => {
  return (
    <Card className={cn("stats-card", className)}>
      <div className="text-agro-600 mb-4">{icon}</div>
      <div className="text-4xl font-bold text-agro-800">{value}</div>
      <div className="text-muted-foreground mt-2 text-center">{label}</div>
    </Card>
  );
};

export default StatsCard;
