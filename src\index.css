
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 29% 97%;
    --foreground: 120 7% 15%;

    --card: 0 0% 100%;
    --card-foreground: 120 7% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 120 7% 15%;

    --primary: 134 30% 40%;
    --primary-foreground: 120 29% 97%;

    --secondary: 80 65% 90%;
    --secondary-foreground: 120 7% 15%;

    --muted: 120 7% 95%;
    --muted-foreground: 120 7% 40%;

    --accent: 72 76% 90%;
    --accent-foreground: 120 7% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 120 29% 97%;

    --border: 120 7% 90%;
    --input: 120 7% 90%;
    --ring: 134 30% 40%;

    --radius: 0.5rem;

    --sidebar-background: 120 29% 97%;
    --sidebar-foreground: 120 7% 15%;
    --sidebar-primary: 134 30% 40%;
    --sidebar-primary-foreground: 120 29% 97%;
    --sidebar-accent: 120 7% 95%;
    --sidebar-accent-foreground: 120 7% 15%;
    --sidebar-border: 120 7% 90%;
    --sidebar-ring: 134 30% 40%;
  }

  .dark {
    --background: 120 7% 10%;
    --foreground: 120 29% 97%;

    --card: 120 7% 12%;
    --card-foreground: 120 29% 97%;

    --popover: 120 7% 12%;
    --popover-foreground: 120 29% 97%;

    --primary: 134 30% 40%;
    --primary-foreground: 120 29% 97%;

    --secondary: 120 7% 20%;
    --secondary-foreground: 120 29% 97%;

    --muted: 120 7% 18%;
    --muted-foreground: 120 7% 65%;

    --accent: 120 7% 20%;
    --accent-foreground: 120 29% 97%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 120 29% 97%;

    --border: 120 7% 18%;
    --input: 120 7% 18%;
    --ring: 134 30% 40%;

    --sidebar-background: 120 7% 10%;
    --sidebar-foreground: 120 29% 97%;
    --sidebar-primary: 134 30% 40%;
    --sidebar-primary-foreground: 120 29% 97%;
    --sidebar-accent: 120 7% 18%;
    --sidebar-accent-foreground: 120 29% 97%;
    --sidebar-border: 120 7% 18%;
    --sidebar-ring: 134 30% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  .hero-section {
    background: 
      radial-gradient(circle at 20% 20%, rgba(72, 187, 120, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #1e3a2e 0%, #2d5a41 25%, #4f9158 50%, #70ab79 75%, #8bc89e 100%);
    position: relative;
    min-height: 100vh;
    @apply overflow-hidden;
  }

  .hero-section::before {
    content: '';
    @apply absolute inset-0;
    background: 
      radial-gradient(circle at 30% 20%, rgba(255,255,255,0.15) 0%, transparent 60%),
      radial-gradient(circle at 70% 80%, rgba(255,255,255,0.1) 0%, transparent 60%),
      radial-gradient(circle at 50% 50%, rgba(72, 187, 120, 0.08) 0%, transparent 70%);
    animation: ethereal-glow 20s ease-in-out infinite;
  }

  /* Grid pattern */
  .grid-pattern {
    background-image: 
      linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    @apply w-full h-full;
  }
  
  /* Enhanced animated blob shapes */
  .blob-shape {
    @apply absolute rounded-full mix-blend-multiply filter blur-2xl;
    animation: blob 25s infinite ease-in-out;
  }
  
  .blob-shape-1 {
    @apply bg-gradient-to-r from-emerald-300 via-green-300 to-teal-300;
    top: 5%;
    left: 5%;
    width: 350px;
    height: 350px;
    opacity: 0.4;
    animation-delay: 0s;
  }
  
  .blob-shape-2 {
    @apply bg-gradient-to-r from-lime-300 via-green-300 to-emerald-300;
    bottom: 5%;
    right: 5%;
    width: 450px;
    height: 450px;
    opacity: 0.3;
    animation-delay: 6s;
  }
  
  .blob-shape-3 {
    @apply bg-gradient-to-r from-teal-300 via-cyan-300 to-emerald-300;
    top: 45%;
    right: 15%;
    width: 300px;
    height: 300px;
    opacity: 0.35;
    animation-delay: 12s;
  }
  
  .blob-shape-4 {
    @apply bg-gradient-to-r from-green-300 via-lime-300 to-yellow-300;
    bottom: 25%;
    left: 15%;
    width: 400px;
    height: 400px;
    opacity: 0.25;
    animation-delay: 18s;
  }
  
  .blob-shape-5 {
    @apply bg-gradient-to-r from-emerald-400 via-green-400 to-teal-400;
    top: 25%;
    left: 45%;
    width: 250px;
    height: 250px;
    opacity: 0.2;
    animation-delay: 24s;
  }

  /* Enhanced floating particles */
  .floating-particle {
    @apply absolute flex items-center justify-center;
    animation: float-enhanced 8s ease-in-out infinite;
  }
  
  .particle-1 {
    top: 15%;
    left: 12%;
    animation-delay: 0s;
  }
  
  .particle-2 {
    top: 65%;
    left: 85%;
    animation-delay: 1.5s;
  }
  
  .particle-3 {
    top: 85%;
    left: 25%;
    animation-delay: 3s;
  }
  
  .particle-4 {
    top: 25%;
    left: 75%;
    animation-delay: 4.5s;
  }
  
  .particle-5 {
    top: 45%;
    left: 55%;
    animation-delay: 6s;
  }
  
  .particle-6 {
    top: 75%;
    left: 65%;
    animation-delay: 7.5s;
  }

  /* Advanced stats section animations */
  .animated-stats-section {
    position: relative;
  }

  /* Animated mesh background */
  .animated-mesh-bg {
    background: 
      radial-gradient(circle at 25% 25%, rgba(52, 211, 153, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
    animation: mesh-flow 30s ease-in-out infinite;
  }

  /* Floating orbs system */
  .particles-container {
    pointer-events: none;
  }

  .floating-orb {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(52, 211, 153, 0.8), rgba(34, 197, 94, 0.6));
    animation: float-orb 15s linear infinite;
    box-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
  }

  .orb-1 { animation-duration: 12s; background: radial-gradient(circle, rgba(52, 211, 153, 0.8), transparent); }
  .orb-2 { animation-duration: 15s; background: radial-gradient(circle, rgba(34, 197, 94, 0.7), transparent); }
  .orb-3 { animation-duration: 18s; background: radial-gradient(circle, rgba(16, 185, 129, 0.6), transparent); }
  .orb-4 { animation-duration: 20s; background: radial-gradient(circle, rgba(5, 150, 105, 0.8), transparent); }
  .orb-5 { animation-duration: 14s; background: radial-gradient(circle, rgba(6, 182, 212, 0.7), transparent); }

  /* Dynamic light rays */
  .light-ray {
    position: absolute;
    width: 2px;
    height: 200px;
    background: linear-gradient(to bottom, transparent, rgba(52, 211, 153, 0.4), transparent);
    transform-origin: top center;
  }

  .ray-1 {
    top: 0;
    left: 20%;
    animation: ray-sweep 25s linear infinite;
    animation-delay: 0s;
  }

  .ray-2 {
    top: 0;
    left: 60%;
    animation: ray-sweep 30s linear infinite;
    animation-delay: 8s;
  }

  .ray-3 {
    top: 0;
    left: 80%;
    animation: ray-sweep 35s linear infinite;
    animation-delay: 15s;
  }

  /* Geometric patterns */
  .geometric-patterns {
    pointer-events: none;
  }

  .rotating-hexagon {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(52, 211, 153, 0.2);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: rotate-geometric 40s linear infinite;
  }

  .hex-1 {
    top: 15%;
    right: 10%;
    animation-delay: 0s;
  }

  .hex-2 {
    bottom: 20%;
    left: 15%;
    animation-delay: 20s;
    transform: scale(0.7);
  }

  .rotating-triangle {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(34, 197, 94, 0.3);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: rotate-geometric-reverse 30s linear infinite;
  }

  .tri-1 {
    top: 40%;
    left: 8%;
    animation-delay: 10s;
  }

  .tri-2 {
    bottom: 40%;
    right: 12%;
    animation-delay: 25s;
    transform: scale(1.2);
  }

  /* Parallax elements */
  .parallax-elements {
    pointer-events: none;
  }

  .parallax-circle {
    position: absolute;
    border-radius: 50%;
    border: 1px solid rgba(52, 211, 153, 0.15);
    animation: parallax-float 50s linear infinite;
  }

  .circle-1 {
    width: 120px;
    height: 120px;
    top: 30%;
    right: 20%;
    animation-delay: 0s;
  }

  .circle-2 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 25%;
    animation-delay: 25s;
  }

  .parallax-square {
    position: absolute;
    border: 1px solid rgba(34, 197, 94, 0.15);
    animation: parallax-float-reverse 45s linear infinite;
  }

  .square-1 {
    width: 50px;
    height: 50px;
    top: 60%;
    right: 30%;
    animation-delay: 15s;
  }

  .square-2 {
    width: 30px;
    height: 30px;
    top: 20%;
    left: 30%;
    animation-delay: 30s;
  }
}

@layer components {
  .nav-link {
    @apply px-3 py-2 text-base font-medium rounded-md transition-colors hover:bg-agro-100 hover:text-agro-800;
  }

  .nav-link.active {
    @apply bg-agro-100 text-agro-800;
  }

  /* Enhanced card hover effects */
  .card-hover {
    @apply transition-all duration-500 hover:shadow-2xl hover:-translate-y-3 relative bg-white overflow-hidden;
  }
  
  .card-hover::after {
    content: '';
    @apply absolute inset-0 top-0 left-0 w-full h-full bg-gradient-to-br from-agro-200/20 to-agro-400/10 opacity-0 transition-opacity duration-500;
  }
  
  .card-hover:hover::after {
    @apply opacity-100;
  }

  /* Enhanced feature cards */
  .feature-card {
    @apply relative overflow-hidden;
  }
  
  .feature-card::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-agro-400 via-green-400 to-agro-600 transform scale-x-0 transition-transform duration-500;
  }
  
  .feature-card:hover::before {
    @apply scale-x-100;
  }
  
  .feature-card-main {
    @apply relative overflow-hidden;
  }
  
  .feature-card-main::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-400 via-green-400 to-teal-400 transform scale-x-0 transition-transform duration-700;
  }
  
  .feature-card-main:hover::before {
    @apply scale-x-100;
  }
  
  .feature-card-main::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-emerald-500/5 opacity-0 transition-opacity duration-500;
  }
  
  .feature-card-main:hover::after {
    @apply opacity-100;
  }

  .stats-card {
    @apply bg-white shadow-lg rounded-xl p-6 flex flex-col items-center justify-center card-hover;
  }
  
  .section-heading {
    @apply text-3xl md:text-4xl font-bold mb-6 text-agro-800 relative inline-block;
  }
  
  .section-heading::after {
    content: '';
    @apply absolute bottom-0 left-0 w-1/3 h-1 bg-gradient-to-r from-agro-400 to-agro-600 rounded;
  }
  
  /* Enhanced button hover effect */
  .btn-hover-slide {
    @apply relative overflow-hidden;
  }
  
  .btn-hover-slide::after {
    content: '';
    @apply absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full transition-transform duration-700;
  }
  
  .btn-hover-slide:hover::after {
    @apply translate-x-full;
  }
  
  /* Enhanced animated lists */
  .animate-list > * {
    @apply opacity-0 translate-y-6 transition-all duration-600;
    animation: fadeInUp 0.6s forwards;
  }
  
  .animate-list > *:nth-child(1) { animation-delay: 0.1s; }
  .animate-list > *:nth-child(2) { animation-delay: 0.2s; }
  .animate-list > *:nth-child(3) { animation-delay: 0.3s; }
  .animate-list > *:nth-child(4) { animation-delay: 0.4s; }
  .animate-list > *:nth-child(5) { animation-delay: 0.5s; }
  .animate-list > *:nth-child(6) { animation-delay: 0.6s; }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blob {
  0%, 100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  20% {
    transform: translate(30px, -50px) scale(1.1) rotate(72deg);
  }
  40% {
    transform: translate(-20px, 30px) scale(0.9) rotate(144deg);
  }
  60% {
    transform: translate(50px, 20px) scale(1.05) rotate(216deg);
  }
  80% {
    transform: translate(-30px, -20px) scale(0.95) rotate(288deg);
  }
}

@keyframes float-enhanced {
  0%, 100% {
    transform: translateY(0) rotateX(0deg) rotateY(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-15px) rotateX(90deg) rotateY(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-25px) rotateX(180deg) rotateY(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-15px) rotateX(270deg) rotateY(270deg);
    opacity: 0.8;
  }
}

@keyframes ethereal-glow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Advanced animation keyframes for stats section */
@keyframes mesh-flow {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateX(-20px) translateY(-10px) scale(1.05);
    opacity: 0.8;
  }
  50% {
    transform: translateX(15px) translateY(20px) scale(0.95);
    opacity: 1;
  }
  75% {
    transform: translateX(-10px) translateY(-15px) scale(1.02);
    opacity: 0.9;
  }
}

@keyframes float-orb {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) translateX(0) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(-10vh) translateX(20px) scale(1);
  }
  100% {
    transform: translateY(-20vh) translateX(40px) scale(0);
    opacity: 0;
  }
}

@keyframes ray-sweep {
  0% {
    transform: rotate(-45deg) scaleY(0);
    opacity: 0;
  }
  25% {
    transform: rotate(-15deg) scaleY(1);
    opacity: 1;
  }
  50% {
    transform: rotate(15deg) scaleY(1);
    opacity: 0.8;
  }
  75% {
    transform: rotate(45deg) scaleY(1);
    opacity: 1;
  }
  100% {
    transform: rotate(75deg) scaleY(0);
    opacity: 0;
  }
}

@keyframes rotate-geometric {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.2;
  }
  50% {
    transform: rotate(180deg) scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.2;
  }
}

@keyframes rotate-geometric-reverse {
  0% {
    transform: rotate(360deg) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: rotate(180deg) scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
}

@keyframes parallax-float {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-30px) translateX(20px);
  }
  50% {
    transform: translateY(-60px) translateX(0);
  }
  75% {
    transform: translateY(-30px) translateX(-20px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

@keyframes parallax-float-reverse {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
  }
  25% {
    transform: translateY(30px) translateX(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(60px) translateX(0) rotate(180deg);
  }
  75% {
    transform: translateY(30px) translateX(20px) rotate(270deg);
  }
  100% {
    transform: translateY(0) translateX(0) rotate(360deg);
  }
}

@keyframes text-reveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes counter-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(52, 211, 153, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(52, 211, 153, 0.6);
  }
}

@keyframes icon-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes float-delayed-2 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  33% {
    transform: translateY(-8px) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: translateY(-5px) rotate(240deg);
    opacity: 1;
  }
}

@keyframes shimmer-pass {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(300%) skewX(-12deg);
  }
}

@keyframes border-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes wave-morph {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100px);
  }
}

@keyframes wave-flow {
  0% {
    d: path("M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,58.7C672,64,768,96,864,106.7C960,117,1056,107,1152,96C1248,85,1344,75,1392,69.3L1440,64L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z");
  }
  50% {
    d: path("M0,80L48,85.3C96,91,192,101,288,96C384,91,480,69,576,74.7C672,80,768,112,864,122.7C960,133,1056,123,1152,112C1248,101,1344,91,1392,85.3L1440,80L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z");
  }
  100% {
    d: path("M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,58.7C672,64,768,96,864,106.7C960,117,1056,107,1152,96C1248,85,1344,75,1392,69.3L1440,64L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z");
  }
}

@layer utilities {
  .animate-blob {
    animation: blob 25s infinite ease-in-out;
  }
  
  .animate-float {
    animation: float-enhanced 8s ease-in-out infinite;
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
  
  .animate-ethereal {
    animation: ethereal-glow 20s ease-in-out infinite;
  }

  .animate-text-reveal {
    animation: text-reveal 1s ease-out forwards;
  }

  .animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient-x 3s ease infinite;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animate-counter-up {
    animation: counter-up 1s ease-out forwards;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-icon-float {
    animation: icon-float 3s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
  }

  .animate-float-delayed-2 {
    animation: float-delayed-2 5s ease-in-out infinite;
  }

  .animate-shimmer-pass {
    animation: shimmer-pass 1.5s ease-out;
  }

  .animate-border-flow {
    background-size: 200% 200%;
    animation: border-flow 3s linear infinite;
  }

  .animate-wave-morph {
    animation: wave-morph 8s ease-in-out infinite;
  }

  .animate-wave-flow {
    animation: wave-flow 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-spin-slow {
    animation: spin 8s linear infinite;
  }
}
