
import { cn } from "@/lib/utils";

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  center?: boolean;
  className?: string;
}

const SectionHeader = ({ title, subtitle, center = false, className }: SectionHeaderProps) => {
  return (
    <div className={cn(center && "text-center", "mb-10", className)}>
      <h2 className="section-heading">{title}</h2>
      {subtitle && <p className="text-muted-foreground text-lg mt-3">{subtitle}</p>}
    </div>
  );
};

export default SectionHeader;
