
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, Leaf, BookO<PERSON>, Users, Sparkles, TrendingUp, <PERSON>, Award, CheckCircle } from "lucide-react";

const Hero = () => {
  return (
    <section className="hero-section relative overflow-hidden">
      {/* Enhanced animated blob shapes */}
      <div className="blob-shape blob-shape-1"></div>
      <div className="blob-shape blob-shape-2"></div>
      <div className="blob-shape blob-shape-3"></div>
      <div className="blob-shape blob-shape-4"></div>
      <div className="blob-shape blob-shape-5"></div>
      
      {/* Enhanced floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="floating-particle particle-1">
          <Leaf className="h-4 w-4 text-white/60" />
        </div>
        <div className="floating-particle particle-2">
          <Star className="h-3 w-3 text-white/50" />
        </div>
        <div className="floating-particle particle-3">
          <Sparkles className="h-4 w-4 text-white/60" />
        </div>
        <div className="floating-particle particle-4">
          <Award className="h-3 w-3 text-white/50" />
        </div>
        <div className="floating-particle particle-5">
          <CheckCircle className="h-4 w-4 text-white/60" />
        </div>
        <div className="floating-particle particle-6">
          <Leaf className="h-3 w-3 text-white/40" />
        </div>
      </div>
      
      {/* Decorative grid pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid-pattern"></div>
      </div>
      
      <div className="relative py-32 md:py-40 lg:py-48 px-6 container mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left content - Enhanced */}
          <div className="space-y-10 text-white">
            <div className="space-y-8">
              {/* Enhanced badge */}
              <div className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-lg text-white text-sm font-medium border border-white/30 hover:bg-white/30 transition-all duration-500 cursor-pointer group shadow-xl">
                <span className="flex h-3 w-3 mr-3">
                  <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-green-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500 shadow-lg"></span>
                </span>
                Formation d'excellence en Agriculture
                <Sparkles className="ml-3 h-4 w-4 group-hover:animate-spin transition-transform duration-300" />
              </div>
              
              {/* Enhanced headings */}
              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold animate-fade-in tracking-tight leading-tight">
                  <span className="bg-gradient-to-r from-white via-green-100 to-green-200 bg-clip-text text-transparent animate-pulse-slow drop-shadow-lg">
                    Pôle Agriculture
                  </span>
                </h1>
                
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-light animate-fade-in text-green-100 drop-shadow-md">
                  Cité des Métiers et des Compétences
                </h2>
              </div>
              
              {/* Enhanced description */}
              <p className="text-lg md:text-xl animate-fade-in leading-relaxed text-white/95 max-w-2xl drop-shadow-sm">
                Formez-vous aux métiers d'avenir dans le secteur agricole avec des programmes innovants et adaptés aux besoins du marché marocain. Rejoignez l'excellence agricole.
              </p>
              
              {/* Enhanced action buttons */}
              <div className="flex flex-col sm:flex-row gap-6 animate-fade-in pt-6">
                <Button asChild size="lg" className="bg-white text-agro-800 hover:bg-gray-100 btn-hover-slide group relative overflow-hidden shadow-2xl hover:shadow-white/20 transition-all duration-300">
                  <Link to="/formations" className="relative z-10">
                    <TrendingUp className="mr-2 h-5 w-5" />
                    Découvrir nos formations
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="text-white border-white/60 hover:bg-white/20 hover:text-white backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                  <Link to="/presentation">En savoir plus</Link>
                </Button>
              </div>
              
              {/* New stats preview */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-white/20">
                <div className="text-center group">
                  <div className="text-2xl md:text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">500+</div>
                  <div className="text-sm text-white/80">Stagiaires</div>
                </div>
                <div className="text-center group">
                  <div className="text-2xl md:text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">12</div>
                  <div className="text-sm text-white/80">Formations</div>
                </div>
                <div className="text-center group">
                  <div className="text-2xl md:text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">15</div>
                  <div className="text-sm text-white/80">Partenariats</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right content - Enhanced feature cards */}
          <div className="relative lg:justify-self-end">
            <div className="grid gap-8 max-w-lg">
              {/* Main feature card - Enhanced */}
              <div className="feature-card-main bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-2xl transform hover:scale-105 transition-all duration-500 hover:shadow-green-500/30 border border-white/20">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="bg-gradient-to-br from-agro-400 to-agro-600 p-4 rounded-2xl shadow-lg">
                    <Leaf className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-agro-800">Agriculture durable</h3>
                    <p className="text-agro-600 flex items-center">
                      Innovation & Excellence 
                      <Star className="ml-1 h-4 w-4 text-yellow-500" />
                    </p>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed mb-6">
                  Maîtrisez les pratiques agricoles respectueuses de l'environnement pour un avenir durable et prospère.
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-agro-600 font-medium hover:text-agro-700 transition-colors cursor-pointer">
                    <span>En savoir plus</span>
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </div>
                  <div className="flex space-x-1">
                    {[1,2,3,4,5].map((i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Secondary cards - Enhanced */}
              <div className="grid grid-cols-2 gap-6">
                <div className="feature-card bg-gradient-to-br from-white/95 to-white/85 backdrop-blur-md rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-3 group border border-white/10">
                  <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-3 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-md">
                    <BookOpen className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">Expertise technique</h4>
                  <p className="text-sm text-gray-600">Formation pratique et théorique de haut niveau</p>
                  <div className="mt-3 flex items-center text-xs text-blue-600">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Certifié
                  </div>
                </div>
                
                <div className="feature-card bg-gradient-to-br from-white/95 to-white/85 backdrop-blur-md rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-3 group border border-white/10">
                  <div className="bg-gradient-to-br from-purple-100 to-purple-200 p-3 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-md">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">Réseau professionnel</h4>
                  <p className="text-sm text-gray-600">Partenariats avec les leaders du secteur</p>
                  <div className="mt-3 flex items-center text-xs text-purple-600">
                    <Award className="mr-1 h-3 w-3" />
                    Reconnu
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced wave shape divider */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg
          className="relative block w-full h-20 md:h-24"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.9)" />
              <stop offset="50%" stopColor="rgba(255,255,255,1)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0.9)" />
            </linearGradient>
          </defs>
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V0C115.39,13.93,252.2,46.79,321.39,56.44Z"
            fill="url(#waveGradient)"
          />
        </svg>
      </div>
    </section>
  );
};

export default Hero;
