
import React, { useState } from "react";
import SectionHeader from "@/components/ui/section-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { 
  Calendar, 
  FilePlus, 
  Clock, 
  User, 
  PenSquare, 
  Trash2, 
  Plus, 
  FileText, 
  BarChart, 
  BookOpen,
  Users,
  GraduationCap,
  Building,
  Calendar as CalendarIcon,
  FileEdit,
  Loader2,
  Search,
  Mail,
  Phone
} from "lucide-react";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [searchTerm, setSearchTerm] = useState("");

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // Pour la démonstration, on simule une connexion réussie
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'espace administrateur.",
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Modification enregistrée",
      description: "Les changements ont été effectués avec succès.",
    });
  };

  // Données pour les actualités
  const actualites = [
    {
      id: 1,
      title: "Inauguration du nouveau laboratoire",
      date: "05/03/2025",
      category: "infrastructure",
      status: "publié"
    },
    {
      id: 2,
      title: "Journée portes ouvertes",
      date: "15/04/2025",
      category: "evenement",
      status: "programmé"
    },
    {
      id: 3,
      title: "Partenariat avec OCP Group",
      date: "03/03/2025",
      category: "partenariat",
      status: "publié"
    },
    {
      id: 4,
      title: "Lancement du programme d'agriculture durable",
      date: "20/02/2025",
      category: "formation",
      status: "publié"
    },
    {
      id: 5,
      title: "Conférence internationale sur l'agriculture",
      date: "05/05/2025",
      category: "evenement",
      status: "programmé"
    }
  ];

  // Données pour les étudiants
  const etudiants = [
    {
      id: "ST2025001",
      nom: "Saidi",
      prenom: "Karim",
      formation: "Culture",
      groupe: "Groupe 1",
      statut: "actif"
    },
    {
      id: "ST2025002",
      nom: "Alaoui",
      prenom: "Fatima",
      formation: "Agriculture durable",
      groupe: "Groupe 1",
      statut: "actif"
    },
    {
      id: "ST2025003",
      nom: "Benjelloun",
      prenom: "Ahmed",
      formation: "Élevage",
      groupe: "Groupe 2",
      statut: "actif"
    },
    {
      id: "ST2025004",
      nom: "Tazi",
      prenom: "Leila",
      formation: "Gestion d'exploitation",
      groupe: "Groupe 1",
      statut: "actif"
    },
    {
      id: "ST2025005",
      nom: "Rami",
      prenom: "Omar",
      formation: "Culture",
      groupe: "Groupe 2",
      statut: "actif"
    }
  ];

  // Données pour les formations
  const formations = [
    {
      id: 1,
      titre: "Techniques de culture",
      niveau: "Technicien",
      duree: "12 mois",
      places: 25,
      inscrits: 22,
      statut: "en cours"
    },
    {
      id: 2,
      titre: "Élevage et production animale",
      niveau: "Technicien",
      duree: "12 mois",
      places: 25,
      inscrits: 18,
      statut: "en cours"
    },
    {
      id: 3,
      titre: "Gestion d'exploitation agricole",
      niveau: "Technicien spécialisé",
      duree: "18 mois",
      places: 20,
      inscrits: 15,
      statut: "en cours"
    },
    {
      id: 4,
      titre: "Agriculture durable et agroécologie",
      niveau: "Technicien spécialisé",
      duree: "18 mois",
      places: 20,
      inscrits: 20,
      statut: "en cours"
    },
    {
      id: 5,
      titre: "Mécanisation agricole",
      niveau: "Technicien",
      duree: "12 mois",
      places: 20,
      inscrits: 0,
      statut: "à venir"
    }
  ];

  // Données pour les emplois du temps
  const emploisTemps = [
    {
      id: 1,
      formation: "Techniques de culture",
      groupe: "Groupe 1",
      derniereMaj: "01/09/2025",
      status: "actif"
    },
    {
      id: 2,
      formation: "Techniques de culture",
      groupe: "Groupe 2",
      derniereMaj: "01/09/2025",
      status: "actif"
    },
    {
      id: 3,
      formation: "Élevage et production animale",
      groupe: "Groupe 1",
      derniereMaj: "01/09/2025",
      status: "actif"
    },
    {
      id: 4,
      formation: "Élevage et production animale",
      groupe: "Groupe 2",
      derniereMaj: "01/09/2025",
      status: "actif"
    },
    {
      id: 5,
      formation: "Gestion d'exploitation agricole",
      groupe: "Groupe 1",
      derniereMaj: "01/09/2025",
      status: "actif"
    }
  ];

  // Données pour le tableau de bord
  const statsCards = [
    {
      title: "Étudiants inscrits",
      value: 480,
      icon: <Users className="h-6 w-6 text-agro-600" />,
      change: "+12%",
    },
    {
      title: "Formations actives",
      value: 12,
      icon: <BookOpen className="h-6 w-6 text-agro-600" />,
      change: "+2",
    },
    {
      title: "Taux de réussite",
      value: "92%",
      icon: <GraduationCap className="h-6 w-6 text-agro-600" />,
      change: "+5%",
    },
    {
      title: "Partenaires",
      value: 24,
      icon: <Building className="h-6 w-6 text-agro-600" />,
      change: "+3",
    },
  ];

  // Filtrage des données en fonction de la recherche
  const filteredActualites = actualites.filter(actualite => 
    actualite.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    actualite.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredEtudiants = etudiants.filter(etudiant => 
    etudiant.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    etudiant.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    etudiant.formation.toLowerCase().includes(searchTerm.toLowerCase()) ||
    etudiant.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFormations = formations.filter(formation => 
    formation.titre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formation.niveau.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredEmploisTemps = emploisTemps.filter(emploi => 
    emploi.formation.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emploi.groupe.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div>
      <section className="bg-agro-800 text-white py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Espace Administrateur</h1>
            <p className="text-xl mb-0">
              Gérez les contenus du site, les formations, les emplois du temps et les stagiaires.
            </p>
          </div>
        </div>
      </section>

      {!isLoggedIn ? (
        <section className="py-16 container mx-auto px-6">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Connexion administrateur</CardTitle>
                <CardDescription>
                  Veuillez vous connecter pour accéder au panel d'administration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      id="email" 
                      type="email"
                      placeholder="<EMAIL>" 
                      value={loginData.email}
                      onChange={(e) => setLoginData({...loginData, email: e.target.value})}
                      required 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Mot de passe</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      placeholder="Votre mot de passe" 
                      value={loginData.password}
                      onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                      required 
                    />
                  </div>
                  <Button type="submit" className="w-full">Se connecter</Button>
                </form>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button variant="link" className="px-0">Mot de passe oublié ?</Button>
              </CardFooter>
            </Card>
          </div>
        </section>
      ) : (
        <>
          <section className="py-8 bg-agro-50">
            <div className="container mx-auto px-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="mb-4 sm:mb-0">
                  <h2 className="text-xl font-semibold">Bienvenue, Administrateur</h2>
                  <p className="text-muted-foreground">
                    Dernière connexion: {new Date().toLocaleDateString()}
                  </p>
                </div>
                <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
              </div>
            </div>
          </section>

          <section className="py-8 container mx-auto px-6">
            <Tabs defaultValue="dashboard">
              <TabsList className="w-full max-w-4xl mx-auto mb-8">
                <TabsTrigger value="dashboard">Tableau de bord</TabsTrigger>
                <TabsTrigger value="actualites">Actualités</TabsTrigger>
                <TabsTrigger value="formations">Formations</TabsTrigger>
                <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
                <TabsTrigger value="etudiants">Stagiaires</TabsTrigger>
              </TabsList>
              
              <TabsContent value="dashboard">
                <SectionHeader 
                  title="Tableau de bord" 
                  subtitle="Vue d'ensemble des activités du Pôle Agriculture"
                  center
                />
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {statsCards.map((stat, index) => (
                    <Card key={index} className="card-hover">
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between mb-2">
                          <div className="bg-agro-100 p-3 rounded-full">
                            {stat.icon}
                          </div>
                          <Badge variant="outline" className={`${stat.change.includes('+') ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                            {stat.change}
                          </Badge>
                        </div>
                        <div className="text-3xl font-bold mt-4">{stat.value}</div>
                        <div className="text-muted-foreground mt-1">{stat.title}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle>Actualités récentes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Titre</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Catégorie</TableHead>
                            <TableHead>Statut</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {actualites.slice(0, 4).map((actualite) => (
                            <TableRow key={actualite.id}>
                              <TableCell className="font-medium">{actualite.title}</TableCell>
                              <TableCell>{actualite.date}</TableCell>
                              <TableCell>{actualite.category}</TableCell>
                              <TableCell>
                                <Badge className={actualite.status === 'publié' ? 'bg-green-500' : 'bg-amber-500'}>
                                  {actualite.status}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="w-full">Voir toutes les actualités</Button>
                    </CardFooter>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Événements à venir</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex">
                          <div className="flex-shrink-0 bg-agro-100 rounded-md p-3 mr-4 text-center min-w-16">
                            <div className="text-agro-700 font-semibold text-sm">AVR</div>
                            <div className="text-2xl font-bold">15</div>
                          </div>
                          <div>
                            <div className="font-semibold">Journée portes ouvertes</div>
                            <div className="text-sm text-muted-foreground">9h - 17h</div>
                          </div>
                        </div>
                        <div className="flex">
                          <div className="flex-shrink-0 bg-agro-100 rounded-md p-3 mr-4 text-center min-w-16">
                            <div className="text-agro-700 font-semibold text-sm">MAI</div>
                            <div className="text-2xl font-bold">5</div>
                          </div>
                          <div>
                            <div className="font-semibold">Conférence internationale</div>
                            <div className="text-sm text-muted-foreground">8h30 - 18h</div>
                          </div>
                        </div>
                        <div className="flex">
                          <div className="flex-shrink-0 bg-agro-100 rounded-md p-3 mr-4 text-center min-w-16">
                            <div className="text-agro-700 font-semibold text-sm">MAI</div>
                            <div className="text-2xl font-bold">20</div>
                          </div>
                          <div>
                            <div className="font-semibold">Atelier d'agriculture urbaine</div>
                            <div className="text-sm text-muted-foreground">14h - 17h</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="w-full">Gérer les événements</Button>
                    </CardFooter>
                  </Card>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Inscriptions par formation</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {formations.slice(0, 4).map((formation) => (
                          <div key={formation.id}>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">{formation.titre}</span>
                              <span className="text-sm font-medium">{formation.inscrits}/{formation.places}</span>
                            </div>
                            <div className="h-2 bg-agro-100 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-agro-600 rounded-full" 
                                style={{ width: `${(formation.inscrits / formation.places) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Actions rapides</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <Button className="w-full flex flex-col items-center py-6">
                          <FilePlus className="h-6 w-6 mb-2" />
                          <span>Nouvelle actualité</span>
                        </Button>
                        <Button className="w-full flex flex-col items-center py-6">
                          <User className="h-6 w-6 mb-2" />
                          <span>Nouvel étudiant</span>
                        </Button>
                        <Button className="w-full flex flex-col items-center py-6">
                          <Calendar className="h-6 w-6 mb-2" />
                          <span>Modifier emploi</span>
                        </Button>
                        <Button className="w-full flex flex-col items-center py-6">
                          <BarChart className="h-6 w-6 mb-2" />
                          <span>Statistiques</span>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="actualites">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
                  <SectionHeader 
                    title="Gestion des actualités" 
                    subtitle="Ajoutez, modifiez ou supprimez des actualités et événements"
                    className="mb-0"
                  />
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Rechercher une actualité..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-full sm:w-[300px]"
                      />
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="mr-2 h-4 w-4" /> Nouvelle actualité
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Ajouter une nouvelle actualité</DialogTitle>
                          <DialogDescription>
                            Remplissez le formulaire pour créer une nouvelle actualité ou un événement.
                          </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleFormSubmit} className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="title">Titre</Label>
                            <Input id="title" placeholder="Titre de l'actualité" required />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="category">Catégorie</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionnez une catégorie" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="evenement">Événement</SelectItem>
                                  <SelectItem value="formation">Formation</SelectItem>
                                  <SelectItem value="partenariat">Partenariat</SelectItem>
                                  <SelectItem value="infrastructure">Infrastructure</SelectItem>
                                  <SelectItem value="reussite">Réussite</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="date">Date</Label>
                              <Input type="date" id="date" required />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="excerpt">Résumé</Label>
                            <Textarea 
                              id="excerpt" 
                              placeholder="Bref résumé de l'actualité" 
                              className="resize-none" 
                              rows={2} 
                              required 
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="content">Contenu</Label>
                            <Textarea 
                              id="content" 
                              placeholder="Contenu détaillé de l'actualité" 
                              className="resize-none" 
                              rows={6} 
                              required 
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="image">Image</Label>
                            <Input id="image" type="file" accept="image/*" />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch id="publish" />
                            <Label htmlFor="publish">Publier immédiatement</Label>
                          </div>
                          <DialogFooter>
                            <Button type="submit">Enregistrer l'actualité</Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
                
                <Card>
                  <CardContent className="pt-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Titre</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Catégorie</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredActualites.length > 0 ? (
                          filteredActualites.map((actualite) => (
                            <TableRow key={actualite.id}>
                              <TableCell className="font-medium">{actualite.title}</TableCell>
                              <TableCell>{actualite.date}</TableCell>
                              <TableCell>{actualite.category}</TableCell>
                              <TableCell>
                                <Badge className={actualite.status === 'publié' ? 'bg-green-500' : 'bg-amber-500'}>
                                  {actualite.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline" size="icon">
                                    <PenSquare className="h-4 w-4" />
                                  </Button>
                                  <Button variant="destructive" size="icon">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              Aucune actualité trouvée
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
                
                <div className="mt-8">
                  <SectionHeader 
                    title="Gestion des statistiques" 
                    subtitle="Mettez à jour les chiffres clés affichés sur la page d'accueil"
                  />
                  
                  <Card>
                    <CardContent className="pt-6">
                      <form onSubmit={handleFormSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="stat-formations">Formations dispensées</Label>
                            <Input id="stat-formations" type="number" defaultValue="12" min="0" required />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="stat-etudiants">Stagiaires inscrits</Label>
                            <Input id="stat-etudiants" type="number" defaultValue="500" min="0" required />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="stat-projets">Projets réalisés</Label>
                            <Input id="stat-projets" type="number" defaultValue="24" min="0" required />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="stat-partenariats">Partenariats</Label>
                            <Input id="stat-partenariats" type="number" defaultValue="15" min="0" required />
                          </div>
                        </div>
                        <Button type="submit">Mettre à jour les statistiques</Button>
                      </form>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="formations">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
                  <SectionHeader 
                    title="Gestion des formations" 
                    subtitle="Ajoutez, modifiez ou supprimez des formations et gérez les inscriptions"
                    className="mb-0"
                  />
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Rechercher une formation..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-full sm:w-[300px]"
                      />
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="mr-2 h-4 w-4" /> Nouvelle formation
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Ajouter une nouvelle formation</DialogTitle>
                          <DialogDescription>
                            Remplissez le formulaire pour créer une nouvelle formation.
                          </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleFormSubmit} className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="titre">Titre de la formation</Label>
                            <Input id="titre" placeholder="Titre de la formation" required />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="niveau">Niveau</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionnez un niveau" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="technicien">Technicien</SelectItem>
                                  <SelectItem value="technicien-specialise">Technicien spécialisé</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="duree">Durée</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionnez une durée" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="12">12 mois</SelectItem>
                                  <SelectItem value="18">18 mois</SelectItem>
                                  <SelectItem value="24">24 mois</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="places">Nombre de places</Label>
                              <Input id="places" type="number" min="1" defaultValue="20" required />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="date-debut">Date de début</Label>
                              <Input type="date" id="date-debut" required />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea 
                              id="description" 
                              placeholder="Description de la formation" 
                              className="resize-none" 
                              rows={3} 
                              required 
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="prerequisites">Prérequis</Label>
                            <Textarea 
                              id="prerequisites" 
                              placeholder="Prérequis pour suivre la formation" 
                              className="resize-none" 
                              rows={2} 
                              required 
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="programme">Programme</Label>
                            <Textarea 
                              id="programme" 
                              placeholder="Programme de la formation" 
                              className="resize-none" 
                              rows={4} 
                              required 
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch id="active" defaultChecked />
                            <Label htmlFor="active">Formation active</Label>
                          </div>
                          <DialogFooter>
                            <Button type="submit">Enregistrer la formation</Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
                
                <Card>
                  <CardContent className="pt-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Titre</TableHead>
                          <TableHead>Niveau</TableHead>
                          <TableHead>Durée</TableHead>
                          <TableHead>Places</TableHead>
                          <TableHead>Inscrits</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredFormations.length > 0 ? (
                          filteredFormations.map((formation) => (
                            <TableRow key={formation.id}>
                              <TableCell className="font-medium">{formation.titre}</TableCell>
                              <TableCell>{formation.niveau}</TableCell>
                              <TableCell>{formation.duree}</TableCell>
                              <TableCell>{formation.places}</TableCell>
                              <TableCell>{formation.inscrits}</TableCell>
                              <TableCell>
                                <Badge className={formation.statut === 'en cours' ? 'bg-green-500' : 'bg-amber-500'}>
                                  {formation.statut}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline" size="sm">
                                    Inscriptions
                                  </Button>
                                  <Button variant="outline" size="icon">
                                    <PenSquare className="h-4 w-4" />
                                  </Button>
                                  <Button variant="destructive" size="icon">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-4">
                              Aucune formation trouvée
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="emplois">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
                  <SectionHeader 
                    title="Gestion des emplois du temps" 
                    subtitle="Consultez et modifiez les emplois du temps des formations"
                    className="mb-0"
                  />
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Rechercher un emploi du temps..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-full sm:w-[300px]"
                      />
                    </div>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" /> Nouvel emploi du temps
                    </Button>
                  </div>
                </div>
                
                <Card>
                  <CardContent className="pt-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Formation</TableHead>
                          <TableHead>Groupe</TableHead>
                          <TableHead>Dernière mise à jour</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredEmploisTemps.length > 0 ? (
                          filteredEmploisTemps.map((emploi) => (
                            <TableRow key={emploi.id}>
                              <TableCell className="font-medium">{emploi.formation}</TableCell>
                              <TableCell>{emploi.groupe}</TableCell>
                              <TableCell>{emploi.derniereMaj}</TableCell>
                              <TableCell>
                                <Badge className="bg-green-500">
                                  {emploi.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline" size="sm">
                                    <CalendarIcon className="mr-2 h-4 w-4" /> Voir
                                  </Button>
                                  <Button variant="outline" size="icon">
                                    <PenSquare className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              Aucun emploi du temps trouvé
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="etudiants">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
                  <SectionHeader 
                    title="Gestion des stagiaires" 
                    subtitle="Consultez et gérez les dossiers des stagiaires"
                    className="mb-0"
                  />
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Rechercher un stagiaire..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-full sm:w-[300px]"
                      />
                    </div>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" /> Nouveau stagiaire
                    </Button>
                  </div>
                </div>
                
                <Card>
                  <CardContent className="pt-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Nom</TableHead>
                          <TableHead>Prénom</TableHead>
                          <TableHead>Formation</TableHead>
                          <TableHead>Groupe</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredEtudiants.length > 0 ? (
                          filteredEtudiants.map((etudiant) => (
                            <TableRow key={etudiant.id}>
                              <TableCell>{etudiant.id}</TableCell>
                              <TableCell className="font-medium">{etudiant.nom}</TableCell>
                              <TableCell>{etudiant.prenom}</TableCell>
                              <TableCell>{etudiant.formation}</TableCell>
                              <TableCell>{etudiant.groupe}</TableCell>
                              <TableCell>
                                <Badge className="bg-green-500">
                                  {etudiant.statut}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline" size="icon">
                                    <Mail className="h-4 w-4" />
                                  </Button>
                                  <Button variant="outline" size="icon">
                                    <Phone className="h-4 w-4" />
                                  </Button>
                                  <Button variant="outline" size="icon">
                                    <PenSquare className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-4">
                              Aucun stagiaire trouvé
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>
        </>
      )}
    </div>
  );
};

export default Admin;
