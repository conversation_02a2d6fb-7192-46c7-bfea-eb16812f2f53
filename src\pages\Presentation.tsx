
import SectionHeader from "@/components/ui/section-header";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Award, Clock, MessageSquare, Leaf, BookOpen, UserCheck } from "lucide-react";

const Presentation = () => {
  const teamMembers = [
    {
      name: "Dr. <PERSON>",
      role: "Directeur du Pôle Agriculture",
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      bio: "Dr. <PERSON> poss<PERSON> plus de 20 ans d'expérience dans le secteur agricole et la formation professionnelle."
    },
    {
      name: "Pr. <PERSON><PERSON><PERSON>",
      role: "Responsable des Formations",
      image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      bio: "<PERSON>r. <PERSON> supervise le développement et la mise en œuvre des programmes de formation du Pôle Agriculture."
    },
    {
      name: "Dr. Ahmed Tazi",
      role: "Expert en Agriculture Durable",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      bio: "Dr. Tazi est spécialisé dans les techniques d'agriculture durable et d'agroécologie."
    },
    {
      name: "Mme. Nadia Ouazzani",
      role: "Coordinatrice des Relations Professionnelles",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      bio: "Mme. Ouazzani est chargée des partenariats avec les entreprises et les organisations professionnelles."
    }
  ];

  const projects = [
    {
      title: "Ferme pédagogique écologique",
      description: "Création d'une ferme modèle utilisant des pratiques agricoles durables pour la formation pratique des stagiaires.",
      progress: 85,
      participants: 120,
      duration: "Depuis 2023"
    },
    {
      title: "Laboratoire d'innovation agricole",
      description: "Espace dédié à la recherche et au développement de nouvelles techniques agricoles adaptées au contexte marocain.",
      progress: 70,
      participants: 45,
      duration: "Depuis 2024"
    },
    {
      title: "Programme d'accompagnement des jeunes entrepreneurs",
      description: "Soutien aux diplômés pour le lancement de leurs projets agricoles à travers un mentorat et des ressources.",
      progress: 60,
      participants: 30,
      duration: "Depuis 2024"
    }
  ];

  const partners = [
    {
      name: "Ministère de l'Agriculture",
      logo: "https://via.placeholder.com/150",
      description: "Partenaire institutionnel clé pour le développement des programmes de formation."
    },
    {
      name: "OCP Group",
      logo: "https://via.placeholder.com/150",
      description: "Collaboration pour l'innovation dans les techniques agricoles et le financement de projets."
    },
    {
      name: "CGEM",
      logo: "https://via.placeholder.com/150",
      description: "Partenariat pour faciliter l'insertion professionnelle des diplômés."
    },
    {
      name: "Fédération Nationale des Agriculteurs",
      logo: "https://via.placeholder.com/150",
      description: "Collaboration pour les stages pratiques et l'immersion professionnelle."
    }
  ];

  const values = [
    {
      icon: <Award className="h-8 w-8 text-agro-600" />,
      title: "Excellence",
      description: "Nous visons l'excellence dans tous nos programmes de formation et nos projets."
    },
    {
      icon: <Leaf className="h-8 w-8 text-agro-600" />,
      title: "Durabilité",
      description: "Nous promouvons une agriculture respectueuse de l'environnement et économiquement viable."
    },
    {
      icon: <MessageSquare className="h-8 w-8 text-agro-600" />,
      title: "Collaboration",
      description: "Nous croyons en la force du travail d'équipe et des partenariats stratégiques."
    },
    {
      icon: <BookOpen className="h-8 w-8 text-agro-600" />,
      title: "Innovation",
      description: "Nous encourageons l'innovation et l'adoption de nouvelles technologies dans le secteur agricole."
    },
    {
      icon: <UserCheck className="h-8 w-8 text-agro-600" />,
      title: "Employabilité",
      description: "Notre priorité est de former des professionnels prêts à répondre aux besoins du marché du travail."
    }
  ];

  return (
    <div>
      <section className="bg-agro-700 text-white py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Présentation du Pôle Agriculture</h1>
            <p className="text-xl mb-0">
              Découvrez notre histoire, notre mission et notre vision pour l'avenir de la formation agricole au Maroc.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <SectionHeader title="Histoire et mission" />
            <p className="text-lg mb-6">
              Le Pôle Agriculture de la Cité des Métiers et des Compétences a été créé en 2020 dans le cadre de la stratégie nationale de développement de la formation professionnelle au Maroc.
            </p>
            <p className="text-lg mb-6">
              Notre mission est de former des professionnels qualifiés dans le domaine agricole, capables de contribuer au développement du secteur et de répondre aux défis actuels et futurs de l'agriculture marocaine.
            </p>
            <p className="text-lg">
              Nous nous engageons à offrir des formations de qualité, alignées sur les besoins du marché du travail et intégrant les dernières avancées technologiques et pratiques durables dans le secteur agricole.
            </p>
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <img 
              src="https://images.unsplash.com/photo-1570071677470-c04398af73fc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
              alt="Pôle Agriculture" 
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </section>

      <section className="py-16 bg-agro-50">
        <div className="container mx-auto px-6">
          <SectionHeader 
            title="Nos valeurs" 
            subtitle="Les principes qui guident notre action au quotidien"
            center
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="bg-white card-hover">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-4 rounded-full bg-agro-100 p-3">
                      {value.icon}
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{value.title}</h3>
                    <p className="text-muted-foreground">{value.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 container mx-auto px-6">
        <SectionHeader 
          title="Projets en cours" 
          subtitle="Des initiatives innovantes pour une formation pratique et adaptée"
          center
        />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card key={index} className="card-hover">
              <CardContent className="pt-6">
                <h3 className="text-xl font-semibold mb-4">{project.title}</h3>
                <p className="text-muted-foreground mb-6">{project.description}</p>
                <div className="mb-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium">Avancement</span>
                    <span className="text-sm font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <UserCheck className="mr-1 h-4 w-4" />
                    {project.participants} participants
                  </div>
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4" />
                    {project.duration}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <section className="py-16 container mx-auto px-6">
        <SectionHeader 
          title="Notre équipe" 
          subtitle="Des professionnels dédiés à l'excellence dans la formation agricole"
          center
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="overflow-hidden card-hover">
              <div className="h-64 overflow-hidden">
                <img 
                  src={member.image} 
                  alt={member.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardContent className="pt-6">
                <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                <p className="text-agro-600 mb-4">{member.role}</p>
                <p className="text-muted-foreground text-sm">{member.bio}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <section className="py-16 bg-agro-50">
        <div className="container mx-auto px-6">
          <SectionHeader 
            title="Nos partenaires" 
            subtitle="Des collaborations stratégiques pour un écosystème de formation complet"
            center
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {partners.map((partner, index) => (
              <Card key={index} className="bg-white card-hover">
                <CardContent className="pt-6 flex flex-col items-center text-center">
                  <div className="mb-4 w-24 h-24 flex items-center justify-center">
                    <img 
                      src={partner.logo} 
                      alt={partner.name} 
                      className="max-w-full max-h-full"
                    />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{partner.name}</h3>
                  <p className="text-muted-foreground text-sm">{partner.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 container mx-auto px-6">
        <SectionHeader 
          title="Témoignages" 
          subtitle="Ce que disent nos stagiaires et partenaires"
          center
        />
        <Tabs defaultValue="students" className="max-w-3xl mx-auto">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="students">Témoignages de stagiaires</TabsTrigger>
            <TabsTrigger value="partners">Témoignages de partenaires</TabsTrigger>
          </TabsList>
          <TabsContent value="students">
            <Card>
              <CardContent className="pt-6 space-y-6">
                <div className="bg-agro-50 p-6 rounded-lg">
                  <p className="italic mb-4">
                    "La formation au Pôle Agriculture m'a permis d'acquérir des compétences pratiques et théoriques essentielles pour mon projet d'exploitation agricole. Les formateurs sont expérimentés et le matériel pédagogique est de qualité."
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                      <img 
                        src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                        alt="Karim Saidi" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold">Karim Saidi</h4>
                      <p className="text-sm text-muted-foreground">Diplômé en Gestion d'Exploitation Agricole</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-agro-50 p-6 rounded-lg">
                  <p className="italic mb-4">
                    "J'ai particulièrement apprécié l'approche pratique de la formation en agriculture durable. Les projets réels sur lesquels nous avons travaillé m'ont préparée à affronter les défis du secteur agricole actuel."
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                      <img 
                        src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                        alt="Fatima Zahra" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold">Fatima Zahra</h4>
                      <p className="text-sm text-muted-foreground">Diplômée en Agriculture Durable</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="partners">
            <Card>
              <CardContent className="pt-6 space-y-6">
                <div className="bg-agro-50 p-6 rounded-lg">
                  <p className="italic mb-4">
                    "Notre partenariat avec le Pôle Agriculture nous a permis de recruter des jeunes professionnels bien formés et immédiatement opérationnels. La qualité de la formation est excellente."
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                      <img 
                        src="https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                        alt="Omar Benjelloun" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold">Omar Benjelloun</h4>
                      <p className="text-sm text-muted-foreground">Directeur RH, Groupe Agricole Maroc</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-agro-50 p-6 rounded-lg">
                  <p className="italic mb-4">
                    "Les projets collaboratifs menés avec le Pôle Agriculture ont été très fructueux. L'équipe pédagogique est à l'écoute des besoins du secteur et adapte continuellement ses programmes."
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                      <img 
                        src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                        alt="Leila Alaoui" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold">Leila Alaoui</h4>
                      <p className="text-sm text-muted-foreground">Responsable des Partenariats, Fondation pour l'Agriculture</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </section>
    </div>
  );
};

export default Presentation;
