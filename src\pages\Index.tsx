
import Hero from "@/components/home/<USER>";
import StatsCard from "@/components/home/<USER>";
import SectionHeader from "@/components/ui/section-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import { BookOpen, Users, Sprout, Award, Calendar, ArrowRight, Sparkles, TrendingUp, Target } from "lucide-react";

const Index = () => {
  const stats = [
    { 
      icon: <BookOpen size={40} />, 
      value: 12, 
      label: "Formations dispensées" 
    },
    { 
      icon: <Users size={40} />, 
      value: "500+", 
      label: "Stagiaires inscrits" 
    },
    { 
      icon: <Sprout size={40} />, 
      value: 24, 
      label: "Projets réalisés" 
    },
    { 
      icon: <Award size={40} />, 
      value: 15, 
      label: "Partenariats" 
    },
  ];

  const latestNews = [
    {
      id: 1,
      title: "Journée portes ouvertes",
      date: "15 Avril 2025",
      description: "Venez découvrir nos installations et formations lors de notre journée portes ouvertes annuelle.",
      image: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 2,
      title: "Nouveau partenariat avec OCP",
      date: "3 Mars 2025",
      description: "Un nouveau partenariat stratégique a été signé avec l'OCP pour soutenir nos programmes de formation.",
      image: "https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 3,
      title: "Lancement du programme d'agriculture durable",
      date: "20 Février 2025",
      description: "Notre pôle lance un nouveau programme dédié aux techniques d'agriculture durable et écologique.",
      image: "https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    }
  ];

  return (
    <div>
      <Hero />
      
      <section className="py-16 md:py-24 px-6 container mx-auto">
        <SectionHeader 
          title="La Cité des Métiers et des Compétences" 
          subtitle="Une institution d'excellence pour la formation professionnelle au Maroc"
          center
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div>
            <p className="text-lg mb-6">
              La Cité des Métiers et des Compétences (CMC) est un établissement public dédié à la formation professionnelle, offrant des programmes qui répondent aux besoins du marché du travail marocain.
            </p>
            <p className="text-lg mb-6">
              Notre mission est de former les compétences de demain dans divers secteurs stratégiques, dont l'agriculture, secteur vital pour l'économie marocaine.
            </p>
            <Button asChild>
              <Link to="/presentation">En savoir plus sur la CMC</Link>
            </Button>
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <img 
              src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
              alt="Cité des Métiers et des Compétences" 
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        
        <SectionHeader 
          title="Le Pôle Agriculture" 
          subtitle="Formation d'excellence dans le domaine agricole"
          className="mt-16"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="order-2 md:order-1 rounded-lg overflow-hidden shadow-lg">
            <img 
              src="https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
              alt="Pôle Agriculture" 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="order-1 md:order-2">
            <p className="text-lg mb-6">
              Le Pôle Agriculture de la CMC est dédié à la formation des futures générations d'experts agricoles, capables de répondre aux défis du secteur et de contribuer au développement durable du pays.
            </p>
            <p className="text-lg mb-6">
              Nos formations couvrent divers aspects de l'agriculture moderne : techniques de culture, élevage, gestion d'exploitation, et agriculture durable.
            </p>
            <Button asChild>
              <Link to="/formations">Découvrir nos formations</Link>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Ultra-enhanced stats section with advanced animations */}
      <section className="relative py-24 overflow-hidden animated-stats-section">
        {/* Multiple layered backgrounds with different speeds */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-900 via-green-800 to-teal-900"></div>
        
        {/* Animated mesh background */}
        <div className="absolute inset-0 animated-mesh-bg"></div>
        
        {/* Flowing particles system */}
        <div className="absolute inset-0 particles-container">
          {[...Array(25)].map((_, i) => (
            <div 
              key={i} 
              className={`floating-orb orb-${i % 5 + 1}`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 10}s`,
                animationDuration: `${8 + Math.random() * 12}s`
              }}
            ></div>
          ))}
        </div>
        
        {/* Dynamic light rays */}
        <div className="absolute inset-0">
          <div className="light-ray ray-1"></div>
          <div className="light-ray ray-2"></div>
          <div className="light-ray ray-3"></div>
        </div>
        
        {/* Animated geometric patterns */}
        <div className="absolute inset-0 geometric-patterns">
          <div className="rotating-hexagon hex-1"></div>
          <div className="rotating-hexagon hex-2"></div>
          <div className="rotating-triangle tri-1"></div>
          <div className="rotating-triangle tri-2"></div>
        </div>
        
        {/* Parallax moving elements */}
        <div className="absolute inset-0 parallax-elements">
          <div className="parallax-circle circle-1"></div>
          <div className="parallax-circle circle-2"></div>
          <div className="parallax-square square-1"></div>
          <div className="parallax-square square-2"></div>
        </div>
        
        <div className="relative container mx-auto px-6">
          {/* Enhanced header with typing animation */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-8 py-4 mb-8 rounded-full bg-gradient-to-r from-emerald-500/30 to-green-500/30 backdrop-blur-lg border border-emerald-400/40 hover:border-emerald-300/60 transition-all duration-500 group">
              <div className="flex items-center">
                <div className="flex space-x-1 mr-3">
                  <div className="w-2 h-2 bg-emerald-300 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-green-300 rounded-full animate-bounce delay-100"></div>
                  <div className="w-2 h-2 bg-teal-300 rounded-full animate-bounce delay-200"></div>
                </div>
                <Sparkles className="mr-3 h-6 w-6 text-emerald-300 animate-spin-slow" />
                <span className="text-emerald-100 font-semibold text-lg">Excellence en mouvement</span>
                <div className="ml-3 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-transparent rounded-full animate-ping"></div>
                  <div className="relative w-3 h-3 bg-emerald-400 rounded-full"></div>
                </div>
              </div>
            </div>
            
            <h2 className="text-5xl md:text-7xl font-bold text-white mb-8 animate-text-reveal">
              Le Pôle Agriculture en{" "}
              <span className="bg-gradient-to-r from-emerald-300 via-green-300 to-teal-300 bg-clip-text text-transparent animate-gradient-x">
                chiffres
              </span>
            </h2>
            
            <p className="text-xl md:text-2xl text-emerald-100 max-w-4xl mx-auto leading-relaxed animate-fade-in-up delay-300">
              Des résultats concrets qui témoignent de notre engagement envers l'excellence 
              dans la formation agricole et le développement des compétences.
            </p>
          </div>
          
          {/* Ultra-enhanced stats grid with staggered animations */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
            {stats.map((stat, index) => (
              <div key={index} className="group relative animate-fade-in-up" style={{ animationDelay: `${index * 200}ms` }}>
                {/* Multiple layered backgrounds */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/15 to-white/5 rounded-3xl transform group-hover:scale-105 transition-all duration-700"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                
                {/* Main card */}
                <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/20 group-hover:border-emerald-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-emerald-500/30 overflow-hidden">
                  
                  {/* Animated border effect */}
                  <div className="absolute inset-0 rounded-3xl border-2 border-transparent bg-gradient-to-r from-emerald-400 via-green-400 to-teal-400 bg-clip-border opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-border-flow"></div>
                  
                  {/* Icon with multiple effects */}
                  <div className="relative flex items-center justify-center w-20 h-20 mx-auto mb-8 rounded-3xl bg-gradient-to-br from-emerald-400/20 to-green-400/20 border border-emerald-300/30 group-hover:from-emerald-400/40 group-hover:to-green-400/40 group-hover:border-emerald-300/70 transition-all duration-500 group-hover:shadow-lg group-hover:shadow-emerald-400/50 group-hover:animate-pulse-glow">
                    <div className="text-emerald-300 group-hover:text-emerald-100 transition-all duration-500 group-hover:scale-125 transform animate-icon-float">
                      {stat.icon}
                    </div>
                    {/* Ripple effect */}
                    <div className="absolute inset-0 rounded-3xl bg-emerald-400/20 scale-0 group-hover:scale-150 transition-transform duration-700 opacity-0 group-hover:opacity-30"></div>
                  </div>
                  
                  {/* Stats content with counter animation */}
                  <div className="text-center relative z-10">
                    <div className="text-5xl md:text-6xl font-bold text-white mb-4 group-hover:scale-110 transition-transform duration-500 animate-counter-up">
                      {stat.value}
                    </div>
                    <div className="text-emerald-200 font-medium text-xl group-hover:text-emerald-100 transition-colors duration-500">
                      {stat.label}
                    </div>
                  </div>
                  
                  {/* Floating decorative elements */}
                  <div className="absolute top-4 right-4 w-3 h-3 bg-emerald-400/60 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-float-delayed"></div>
                  <div className="absolute bottom-4 left-4 w-2 h-2 bg-green-400/60 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-float-delayed-2"></div>
                  
                  {/* Shimmer effect */}
                  <div className="absolute inset-0 -top-4 -left-4 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 opacity-0 group-hover:opacity-100 group-hover:animate-shimmer-pass"></div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Enhanced CTA section with more animations */}
          <div className="text-center relative">
            <div className="inline-flex flex-col sm:flex-row gap-6 animate-fade-in-up delay-800">
              <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 via-green-500 to-emerald-600 hover:from-emerald-600 hover:via-green-600 hover:to-emerald-700 text-white border-0 shadow-2xl hover:shadow-emerald-500/60 transition-all duration-500 hover:scale-110 animate-pulse-slow group relative overflow-hidden">
                <Link to="/formations" className="flex items-center relative z-10">
                  <TrendingUp className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                  Découvrir nos formations
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-2" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-emerald-300/60 text-emerald-100 hover:bg-emerald-500/30 hover:border-emerald-300 backdrop-blur-sm hover:text-white transition-all duration-500 hover:scale-105 group">
                <Link to="/presentation" className="flex items-center">
                  <Target className="mr-2 h-5 w-5 group-hover:animate-spin" />
                  Notre mission
                </Link>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Enhanced wave with morphing animation */}
        <div className="absolute bottom-0 left-0 w-full overflow-hidden">
          <svg
            className="relative block w-full h-32 animate-wave-morph"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <defs>
              <linearGradient id="enhancedWaveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="rgba(255,255,255,0.9)" />
                <stop offset="25%" stopColor="rgba(255,255,255,1)" />
                <stop offset="50%" stopColor="rgba(255,255,255,0.95)" />
                <stop offset="75%" stopColor="rgba(255,255,255,1)" />
                <stop offset="100%" stopColor="rgba(255,255,255,0.9)" />
              </linearGradient>
            </defs>
            <path
              d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,58.7C672,64,768,96,864,106.7C960,117,1056,107,1152,96C1248,85,1344,75,1392,69.3L1440,64L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"
              fill="url(#enhancedWaveGradient)"
              className="animate-wave-flow"
            />
          </svg>
        </div>
      </section>
      
      <section className="py-16 md:py-24 container mx-auto px-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <SectionHeader 
            title="Actualités et événements" 
            subtitle="Restez informé des dernières nouvelles du Pôle Agriculture"
            className="mb-0"
          />
          <Button asChild variant="outline" className="mt-4 md:mt-0">
            <Link to="/actualites" className="flex items-center">
              Toutes les actualités <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {latestNews.map((news) => (
            <Card key={news.id} className="overflow-hidden card-hover">
              <div className="h-48 overflow-hidden">
                <img 
                  src={news.image} 
                  alt={news.title} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Calendar className="mr-2 h-4 w-4" />
                  {news.date}
                </div>
                <CardTitle>{news.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {news.description}
                </CardDescription>
                <Button asChild variant="link" className="px-0 mt-4">
                  <Link to="/actualites" className="flex items-center">
                    Lire la suite <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
      
      <section className="py-16 bg-agro-900 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à rejoindre nos formations ?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Inscrivez-vous dès maintenant pour développer vos compétences dans le secteur agricole et préparer votre avenir professionnel.
          </p>
          <Button asChild size="lg" className="bg-white text-agro-800 hover:bg-gray-100">
            <Link to="/formations">S'inscrire aux formations</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;
