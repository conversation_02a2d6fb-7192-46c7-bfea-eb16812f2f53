import { useState } from "react";
import SectionHeader from "@/components/ui/section-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import { Calendar, Clock, FileText, ChevronRight, User, ArrowRight, CheckCircle2, AlertCircle } from "lucide-react";

const EspaceEtudiant = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedFiliere, setSelectedFiliere] = useState("");
  const [selectedGroupe, setSelectedGroupe] = useState("");
  const [loginData, setLoginData] = useState({
    identifier: "",
    password: ""
  });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // Pour la démonstration, on simule une connexion réussie
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans votre espace stagiaire.",
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setSelectedFiliere("");
    setSelectedGroupe("");
  };

  const handleFiliereChange = (value: string) => {
    setSelectedFiliere(value);
    setSelectedGroupe("");
  };

  const filieres = [
    { value: "culture", label: "Culture" },
    { value: "elevage", label: "Élevage" },
    { value: "gestion", label: "Gestion d'exploitation" },
    { value: "durable", label: "Agriculture durable" }
  ];

  const groupes = {
    culture: [
      { value: "c1", label: "Culture - Groupe 1" },
      { value: "c2", label: "Culture - Groupe 2" }
    ],
    elevage: [
      { value: "e1", label: "Élevage - Groupe 1" },
      { value: "e2", label: "Élevage - Groupe 2" }
    ],
    gestion: [
      { value: "g1", label: "Gestion - Groupe 1" }
    ],
    durable: [
      { value: "d1", label: "Agriculture durable - Groupe 1" },
      { value: "d2", label: "Agriculture durable - Groupe 2" }
    ]
  };

  type GroupKey = keyof typeof groupes;

  const selectedGroupes = selectedFiliere ? groupes[selectedFiliere as GroupKey] : [];

  const schedule = [
    { day: "Lundi", morning: "Culture des céréales (Salle A101)", afternoon: "TP: Analyse des sols (Labo 2)" },
    { day: "Mardi", morning: "Irrigation moderne (Salle B205)", afternoon: "Projet tutoré" },
    { day: "Mercredi", morning: "Protection des cultures (Salle A101)", afternoon: "Visite exploitation agricole" },
    { day: "Jeudi", morning: "Gestion des récoltes (Salle C304)", afternoon: "TP: Techniques d'irrigation (Terrain)" },
    { day: "Vendredi", morning: "Technologies agricoles (Salle B205)", afternoon: "Évaluation continue (Salle A101)" }
  ];

  const progress = [
    { module: "Introduction à l'agronomie", progress: 100, grade: "16/20" },
    { module: "Préparation et gestion des sols", progress: 85, grade: "14/20" },
    { module: "Techniques d'irrigation moderne", progress: 70, grade: "En cours" },
    { module: "Protection des cultures", progress: 50, grade: "En cours" },
    { module: "Gestion des récoltes", progress: 20, grade: "À venir" },
    { module: "Technologies agricoles", progress: 10, grade: "À venir" }
  ];

  const documents = [
    { title: "Guide de l'étudiant", type: "PDF", size: "2.4 MB", date: "01/09/2025" },
    { title: "Support de cours - Irrigation moderne", type: "PDF", size: "5.1 MB", date: "15/09/2025" },
    { title: "TP - Analyse des sols", type: "DOCX", size: "1.8 MB", date: "22/09/2025" },
    { title: "Calendrier des évaluations", type: "PDF", size: "0.9 MB", date: "10/09/2025" },
    { title: "Règlement intérieur", type: "PDF", size: "1.2 MB", date: "01/09/2025" }
  ];

  return (
    <div>
      <section className="bg-agro-700 text-white py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Espace Stagiaire</h1>
            <p className="text-xl mb-0">
              Accédez à vos cours, emploi du temps et suivez votre progression pédagogique.
            </p>
          </div>
        </div>
      </section>

      {!isLoggedIn ? (
        <section className="py-16 container mx-auto px-6">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Connexion à l'espace stagiaire</CardTitle>
                <CardDescription>
                  Veuillez vous connecter avec vos identifiants pour accéder à votre espace personnel.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="identifier">Identifiant</Label>
                    <Input 
                      id="identifier" 
                      placeholder="Votre numéro de stagiaire ou email" 
                      value={loginData.identifier}
                      onChange={(e) => setLoginData({...loginData, identifier: e.target.value})}
                      required 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Mot de passe</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      placeholder="Votre mot de passe" 
                      value={loginData.password}
                      onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                      required 
                    />
                  </div>
                  <Button type="submit" className="w-full">Se connecter</Button>
                </form>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="link" className="px-0">Mot de passe oublié ?</Button>
                <Button variant="link" className="px-0">Aide</Button>
              </CardFooter>
            </Card>
          </div>
        </section>
      ) : (
        <>
          <section className="py-8 bg-agro-50">
            <div className="container mx-auto px-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="mb-4 sm:mb-0">
                  <h2 className="text-xl font-semibold">Bienvenue, Stagiaire</h2>
                  <p className="text-muted-foreground">
                    Dernière connexion: {new Date().toLocaleDateString()}
                  </p>
                </div>
                <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
              </div>
            </div>
          </section>

          <section className="py-8 container mx-auto px-6">
            {!selectedFiliere || !selectedGroupe ? (
              <div className="max-w-md mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>Sélectionnez votre filière et groupe</CardTitle>
                    <CardDescription>
                      Veuillez choisir votre filière de formation et votre groupe pour accéder à votre emploi du temps et vos documents.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="filiere">Filière</Label>
                      <Select value={selectedFiliere} onValueChange={handleFiliereChange}>
                        <SelectTrigger id="filiere">
                          <SelectValue placeholder="Sélectionnez votre filière" />
                        </SelectTrigger>
                        <SelectContent>
                          {filieres.map((filiere) => (
                            <SelectItem key={filiere.value} value={filiere.value}>
                              {filiere.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {selectedFiliere && (
                      <div className="space-y-2">
                        <Label htmlFor="groupe">Groupe</Label>
                        <Select 
                          value={selectedGroupe} 
                          onValueChange={setSelectedGroupe}
                          disabled={!selectedFiliere}
                        >
                          <SelectTrigger id="groupe">
                            <SelectValue placeholder="Sélectionnez votre groupe" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedGroupes.map((groupe) => (
                              <SelectItem key={groupe.value} value={groupe.value}>
                                {groupe.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button 
                      className="w-full" 
                      disabled={!selectedFiliere || !selectedGroupe}
                    >
                      Confirmer
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            ) : (
              <Tabs defaultValue="emploi-du-temps">
                <TabsList className="w-full max-w-2xl mx-auto">
                  <TabsTrigger value="emploi-du-temps">Emploi du temps</TabsTrigger>
                  <TabsTrigger value="suivi">Suivi pédagogique</TabsTrigger>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
                
                <TabsContent value="emploi-du-temps" className="mt-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2">
                      <SectionHeader 
                        title="Emploi du temps hebdomadaire" 
                        subtitle={`${filieres.find(f => f.value === selectedFiliere)?.label} - ${selectedGroupes.find(g => g.value === selectedGroupe)?.label}`}
                      />
                      
                      <Card>
                        <CardContent className="pt-6">
                          <div className="space-y-4">
                            {schedule.map((day, index) => (
                              <div key={index}>
                                <h3 className="font-semibold text-lg mb-2">{day.day}</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <Card className="bg-agro-50">
                                    <CardHeader className="pb-2">
                                      <div className="text-sm font-medium text-muted-foreground">Matin (8h30-12h30)</div>
                                    </CardHeader>
                                    <CardContent>
                                      <div className="flex items-start">
                                        <div className="mr-2 mt-1">
                                          <Clock className="h-4 w-4 text-agro-600" />
                                        </div>
                                        <div>{day.morning}</div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                  <Card className="bg-agro-50">
                                    <CardHeader className="pb-2">
                                      <div className="text-sm font-medium text-muted-foreground">Après-midi (14h00-18h00)</div>
                                    </CardHeader>
                                    <CardContent>
                                      <div className="flex items-start">
                                        <div className="mr-2 mt-1">
                                          <Clock className="h-4 w-4 text-agro-600" />
                                        </div>
                                        <div>{day.afternoon}</div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div>
                      <SectionHeader 
                        title="Informations" 
                        subtitle="Rappels et alertes"
                      />
                      
                      <div className="space-y-4">
                        <Card className="bg-red-50 border-red-200">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex items-center text-red-700">
                              <AlertCircle className="h-5 w-5 mr-2" />
                              Évaluation à venir
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-red-600">Contrôle continu de Protection des cultures le 22/09/2025</p>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-green-50 border-green-200">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex items-center text-green-700">
                              <CheckCircle2 className="h-5 w-5 mr-2" />
                              Stage confirmé
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-green-600">Votre stage d'observation aura lieu du 10 au 21 novembre 2025</p>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Absences</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-muted-foreground mb-2">Total ce semestre:</p>
                            <div className="flex justify-between mb-1">
                              <span>Absences justifiées</span>
                              <span className="font-medium">2h</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Absences non justifiées</span>
                              <span className="font-medium text-red-500">0h</span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="suivi" className="mt-6">
                  <SectionHeader 
                    title="Suivi pédagogique" 
                    subtitle="Suivez votre progression dans chaque module"
                    center
                  />
                  
                  <div className="max-w-3xl mx-auto">
                    <Card>
                      <CardHeader>
                        <CardTitle>Progression par module</CardTitle>
                        <CardDescription>
                          Formation: {filieres.find(f => f.value === selectedFiliere)?.label} | Année académique: 2025-2026
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {progress.map((module, index) => (
                            <div key={index}>
                              <div className="flex justify-between mb-2">
                                <div className="font-medium">{module.module}</div>
                                <div className={`font-medium ${module.grade === "À venir" ? "text-muted-foreground" : (module.grade === "En cours" ? "text-amber-500" : "text-green-600")}`}>
                                  {module.grade}
                                </div>
                              </div>
                              <div className="space-y-2">
                                <Progress value={module.progress} className="h-2" />
                                <div className="flex justify-between text-sm text-muted-foreground">
                                  <span>Progression: {module.progress}%</span>
                                  <span>{module.progress === 100 ? "Complété" : "En cours"}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter>
                        <div className="w-full flex justify-between text-sm">
                          <div>Progression globale: <span className="font-medium">56%</span></div>
                          <div>Moyenne générale: <span className="font-medium">15/20</span></div>
                        </div>
                      </CardFooter>
                    </Card>
                    
                    <div className="mt-8">
                      <h3 className="text-xl font-semibold mb-4">Projets et travaux pratiques</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="card-hover">
                          <CardHeader>
                            <CardTitle className="text-lg">Projet tutoré: Optimisation de l'irrigation</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Statut:</span>
                                <span className="font-medium text-amber-500">En cours</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Date de remise:</span>
                                <span>15/11/2025</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Avancement:</span>
                                <span>40%</span>
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter>
                            <Button className="w-full">Voir les détails</Button>
                          </CardFooter>
                        </Card>
                        <Card className="card-hover">
                          <CardHeader>
                            <CardTitle className="text-lg">TP: Analyse des sols</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Statut:</span>
                                <span className="font-medium text-green-600">Évalué</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Date de remise:</span>
                                <span>25/09/2025</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Note:</span>
                                <span className="font-medium">17/20</span>
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter>
                            <Button className="w-full">Voir le compte-rendu</Button>
                          </CardFooter>
                        </Card>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="documents" className="mt-6">
                  <SectionHeader 
                    title="Documents pédagogiques" 
                    subtitle="Accédez à vos supports de cours et ressources pédagogiques"
                    center
                  />
                  
                  <div className="max-w-3xl mx-auto">
                    <Card>
                      <CardHeader>
                        <CardTitle>Documents disponibles</CardTitle>
                        <CardDescription>
                          Documents pour la formation {filieres.find(f => f.value === selectedFiliere)?.label}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {documents.map((doc, index) => (
                            <div key={index} className="flex items-center justify-between p-3 border rounded-md hover:bg-agro-50 transition-colors">
                              <div className="flex items-center">
                                <div className="bg-agro-100 p-2 rounded mr-4">
                                  <FileText className="h-5 w-5 text-agro-600" />
                                </div>
                                <div>
                                  <div className="font-medium">{doc.title}</div>
                                  <div className="text-sm text-muted-foreground">{doc.type} | {doc.size}</div>
                                </div>
                              </div>
                              <div className="flex items-center">
                                <div className="text-sm text-muted-foreground mr-4">{doc.date}</div>
                                <Button size="sm" variant="outline">
                                  Télécharger
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                    
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Bibliothèque en ligne</CardTitle>
                          <CardDescription>
                            Accédez à la bibliothèque numérique de la CMC
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pb-4">
                          <p className="text-muted-foreground mb-4">
                            Plus de 1000 ressources numériques spécialisées dans l'agriculture et les sciences connexes.
                          </p>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>E-books techniques</span>
                            </li>
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>Revues scientifiques</span>
                            </li>
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>Bases de données agricoles</span>
                            </li>
                          </ul>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full">Accéder à la bibliothèque</Button>
                        </CardFooter>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle>Vidéothèque</CardTitle>
                          <CardDescription>
                            Tutoriels vidéo et démonstrations pratiques
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pb-4">
                          <p className="text-muted-foreground mb-4">
                            Une collection de vidéos pédagogiques pour compléter votre apprentissage théorique.
                          </p>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>Techniques de terrain</span>
                            </li>
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>Utilisation des équipements</span>
                            </li>
                            <li className="flex items-start">
                              <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                              <span>Conférences enregistrées</span>
                            </li>
                          </ul>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full">Accéder aux vidéos</Button>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </section>
        </>
      )}

      <section className="py-16 bg-agro-700 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Besoin d'aide ?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Notre équipe de support est disponible pour répondre à toutes vos questions concernant votre formation.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild className="bg-white text-agro-800 hover:bg-gray-100">
              <a href="mailto:<EMAIL>">Contact par email</a>
            </Button>
            <Button asChild variant="outline" className="text-white border-white hover:bg-white/20">
              <a href="tel:+212522222222">+212 5XX XXX XXX</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default EspaceEtudiant;
